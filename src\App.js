import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
// OrbitControls are imported from the three.js examples.
// Bug fix: Added .js extension to the import path.
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

// Main application component
function App() {
  const mountRef = useRef(null); // Reference to mount the Three.js scene

  // useEffect will run only once when the component mounts.
  useEffect(() => {
    // Store the current mount reference to avoid React hook warnings
    const currentMount = mountRef.current;

    // Define variables within the scope
    let scene, camera, renderer, controls;
    let cakeGroup;
    const pointLights = [];

    // --- Helper Functions ---

    // Function to initialize the scene, camera, and renderer
    const init = () => {
      // Create scene
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x050505); // Very dark gray background

      // Create camera
      camera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
      );
      camera.position.set(-3, 10, 22); // Initial camera position

      // Create renderer
      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setPixelRatio(window.devicePixelRatio);
      renderer.setSize(window.innerWidth, window.innerHeight);
      // Ensure currentMount exists before appending
      if (currentMount) {
        currentMount.appendChild(renderer.domElement);
      }


      // Camera Controls (OrbitControls)
      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true; // For smoother movement
      controls.dampingFactor = 0.05;
      controls.screenSpacePanning = false;
      controls.minDistance = 15; // Minimum zoom distance
      controls.maxDistance = 35; // Maximum zoom distance
      controls.maxPolarAngle = Math.PI / 2; // Prevent camera from going below the ground
      controls.enableZoom = true; // Enable zooming with the mouse wheel
      controls.enablePan = false; // Disable panning
    };

    // Function to add lights to the scene
    const addLights = () => {
      const ambientLight = new THREE.AmbientLight(0x222222); // General dim light
      scene.add(ambientLight);
      
      // Candle lights will be added to the `pointLights` array during cake creation.
    };

    // Function to create a more legible "21" candle
    const createTwentyOneCandle = (yPosition) => {
        const candleGroup = new THREE.Group();
        const candleMaterial = new THREE.MeshStandardMaterial({ color: 0xCCCCCC, roughness: 0.5 });
        const flameMaterial = new THREE.MeshBasicMaterial({ color: 0xFFA500, emissive: 0xFFA500 });
        const flameGeometry = new THREE.ConeGeometry(0.1, 0.3, 8);
        const candleHeight = 1.0;
        const candleThickness = 0.15;
        const numberWidth = 0.6;

        // --- Create the "2" digit ---
        const twoGroup = new THREE.Group();
        // Top part
        const two_top = new THREE.Mesh(new THREE.BoxGeometry(numberWidth, candleThickness, candleThickness), candleMaterial);
        two_top.position.set(0, candleHeight, 0);
        // Right vertical part
        const two_right = new THREE.Mesh(new THREE.BoxGeometry(candleThickness, candleHeight / 2, candleThickness), candleMaterial);
        two_right.position.set(numberWidth / 2 - candleThickness/2, candleHeight * 0.75, 0);
        // Middle part
        const two_middle = new THREE.Mesh(new THREE.BoxGeometry(numberWidth, candleThickness, candleThickness), candleMaterial);
        two_middle.position.set(0, candleHeight / 2, 0);
        // Bottom left vertical part
        const two_left_bottom = new THREE.Mesh(new THREE.BoxGeometry(candleThickness, candleHeight / 2, candleThickness), candleMaterial);
        two_left_bottom.position.set(-numberWidth / 2 + candleThickness/2, candleHeight * 0.25, 0);
        // Bottom part
        const two_bottom = new THREE.Mesh(new THREE.BoxGeometry(numberWidth, candleThickness, candleThickness), candleMaterial);
        two_bottom.position.set(0, 0, 0);
        twoGroup.add(two_top, two_right, two_middle, two_left_bottom, two_bottom);
        
        // --- Create the "1" digit ---
        const oneGroup = new THREE.Group();
        // Main vertical part
        const one_main = new THREE.Mesh(new THREE.BoxGeometry(candleThickness, candleHeight, candleThickness), candleMaterial);
        one_main.position.set(0, candleHeight / 2, 0);
        // Small top serif
        const one_serif = new THREE.Mesh(new THREE.BoxGeometry(numberWidth/2, candleThickness, candleThickness), candleMaterial);
        one_serif.position.set(-numberWidth/4, candleHeight - candleThickness/2, 0);
        oneGroup.add(one_main, one_serif);

        // Position the digits to form "21"
        twoGroup.position.x = -0.6; // "2" on the left
        oneGroup.position.x = 0.3;  // "1" on the right
        
        candleGroup.add(oneGroup, twoGroup);

        // Add flames and lights
        // Flame and light for "1" (on the right)
        const flame1 = new THREE.Mesh(flameGeometry, flameMaterial);
        flame1.position.set(0.3, candleHeight + 0.15, 0);
        candleGroup.add(flame1);

        const light1 = new THREE.PointLight(0xFFD700, 1.5, 8);
        light1.position.set(0.3, candleHeight + 0.3, 0);
        pointLights.push(light1);
        candleGroup.add(light1);

        // Flame and light for "2" (on the left)
        const flame2 = new THREE.Mesh(flameGeometry, flameMaterial);
        flame2.position.set(-0.6, candleHeight + 0.15, 0);
        candleGroup.add(flame2);

        const light2 = new THREE.PointLight(0xFFD700, 1.5, 8);
        light2.position.set(-0.6, candleHeight + 0.3, 0);
        pointLights.push(light2);
        candleGroup.add(light2);

        candleGroup.position.y = yPosition + 0.1; // Place it slightly above the cake tier
        candleGroup.scale.set(0.9, 0.9, 0.9);

        return candleGroup;
    }


    // Function to create the cake, candles, and decorations
    const createCake = () => {
      cakeGroup = new THREE.Group();

      // Materials
      const cakeMaterials = [
        new THREE.MeshStandardMaterial({ color: 0x8B0000, roughness: 0.7, metalness: 0.1 }), // Dark Red
        new THREE.MeshStandardMaterial({ color: 0x800080, roughness: 0.7, metalness: 0.1 }), // Purple
        new THREE.MeshStandardMaterial({ color: 0x4B0082, roughness: 0.7, metalness: 0.1 })  // Indigo
      ];
      const frostingMaterial = new THREE.MeshStandardMaterial({ color: 0xFFFFFF, roughness: 0.5, metalness: 0 });
      const plateMaterial = new THREE.MeshStandardMaterial({ color: 0xAAAAAA, roughness: 0.3, metalness: 0.5 });
      const candleMaterial = new THREE.MeshStandardMaterial({ color: 0xCCCCCC, roughness: 0.5 });
      const flameMaterial = new THREE.MeshBasicMaterial({ color: 0xFFA500, emissive: 0xFFA500 });
      
      // Pre-create sprinkle materials for performance
      const sprinkleMaterials = [
          new THREE.MeshStandardMaterial({ color: 0xFF00FF }), // Pink
          new THREE.MeshStandardMaterial({ color: 0x00FFFF }), // Cyan
          new THREE.MeshStandardMaterial({ color: 0xFFFF00 }), // Yellow
          new THREE.MeshStandardMaterial({ color: 0x00FF00 }), // Green
      ];

      // Properties of the cake layers (bigger sized)
      const cakeTiers = [
        { radius: 4.5, height: 2.0, y: 1.0, candles: 11 }, // Bottom tier: 11 candles
        { radius: 3.2, height: 1.6, y: 2.0 + 0.8, candles: 10 }, // Middle tier: 10 candles
        { radius: 2.2, height: 1.3, y: 2.0 + 1.6 + 0.65, candles: 0 }  // Top tier: 0 candles, "21" will be placed here
      ];

      // Cake plate
      const plateGeometry = new THREE.CylinderGeometry(cakeTiers[0].radius + 0.5, cakeTiers[0].radius + 0.5, 0.2, 32);
      const plateMesh = new THREE.Mesh(plateGeometry, plateMaterial);
      plateMesh.position.y = -0.1;
      cakeGroup.add(plateMesh);

      // Create cake layers, decorations, and candles
      cakeTiers.forEach((tier, index) => {
        // Cake layer
        const cakeGeometry = new THREE.CylinderGeometry(tier.radius, tier.radius, tier.height, 32);
        const cakeMesh = new THREE.Mesh(cakeGeometry, cakeMaterials[index]);
        cakeMesh.position.y = tier.y;
        cakeGroup.add(cakeMesh);

        // Frosting
        const frostingGeometry = new THREE.CylinderGeometry(tier.radius + 0.1, tier.radius + 0.1, 0.15, 32);
        const frostingMesh = new THREE.Mesh(frostingGeometry, frostingMaterial);
        const tierTopY = tier.y + tier.height / 2;
        frostingMesh.position.y = tierTopY + 0.075;
        cakeGroup.add(frostingMesh);

        // Sprinkles
        const numSprinklesPerTier = tier.radius * 10;
        const sprinkleGeometry = new THREE.SphereGeometry(0.05, 8, 8);
        for (let i = 0; i < numSprinklesPerTier; i++) {
          const sprinkleMaterial = sprinkleMaterials[Math.floor(Math.random() * sprinkleMaterials.length)];
          const sprinkle = new THREE.Mesh(sprinkleGeometry, sprinkleMaterial);
          const angle = Math.random() * Math.PI * 2;
          const r = Math.random() * (tier.radius + 0.05);
          sprinkle.position.set(
            r * Math.cos(angle),
            tierTopY + 0.15, // On top of the frosting
            r * Math.sin(angle)
          );
          cakeGroup.add(sprinkle);
        }

        // Normal Candles and Flames
        if (tier.candles > 0) {
            const candleGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.7, 16);
            const flameGeometry = new THREE.ConeGeometry(0.1, 0.3, 8);
            const candleSpacing = (Math.PI * 2) / tier.candles;

            for (let i = 0; i < tier.candles; i++) {
              const angle = i * candleSpacing + (index % 2 === 0 ? 0 : Math.PI / tier.candles);
              const x = tier.radius * 0.8 * Math.cos(angle);
              const z = tier.radius * 0.8 * Math.sin(angle);
              const y = tierTopY;

              const candle = new THREE.Mesh(candleGeometry, candleMaterial);
              candle.position.set(x, y + 0.35, z);
              cakeGroup.add(candle);

              const flame = new THREE.Mesh(flameGeometry, flameMaterial);
              flame.position.set(x, y + 0.7, z);
              cakeGroup.add(flame);

              const pointLight = new THREE.PointLight(0xFFD700, 1.2, 7); 
              pointLight.position.set(x, y + 1.0, z);
              scene.add(pointLight);
              pointLights.push(pointLight);
            }
        }
        
        // Add the "21" candle to the top tier
        if (index === cakeTiers.length - 1) {
            const twentyOneCandle = createTwentyOneCandle(tierTopY);
            cakeGroup.add(twentyOneCandle);
        }
      });
      
      cakeGroup.position.y = 1;
      scene.add(cakeGroup);
    };

    // Animation loop
    const animate = () => {
      // Check if renderer is still valid before continuing the loop
      if (!renderer.domElement.parentElement) return;

      requestAnimationFrame(animate);

      // Flicker effect for candle lights
      pointLights.forEach(light => {
        light.intensity = 1.2 + Math.sin(Date.now() * 0.005 + light.position.x) * 0.2;
      });

      // Update camera controls
      controls.update();

      renderer.render(scene, camera);
    };

    // Function to run on window resize
    const handleResize = () => {
      if (!camera || !renderer || !cakeGroup) return; // Guard clause
      const width = window.innerWidth;
      const height = window.innerHeight;

      // Update camera and renderer
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
      
      // Adjust cake and text position based on screen size
      if (width < 768) { // Mobile view
        cakeGroup.position.set(0, 0, 0);
        camera.position.set(0, 8, 20);
      } else { // Desktop view
        cakeGroup.position.set(6, 1, 0); // Position cake further to the right
        camera.position.set(-3, 10, 22); // Adjust camera to see both text and cake
      }
       controls.update(); // Update controls as well
    };

    // --- Setup and Cleanup ---
    
    // Call setup functions
    init();
    addLights();
    createCake();
    handleResize(); // Set layout on initial load
    animate();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      if (currentMount && renderer.domElement) {
        currentMount.removeChild(renderer.domElement);
      }
      
      // Dispose of all objects and materials to prevent memory leaks
      scene.traverse((object) => {
        if (object.isMesh) {
          if (object.geometry) object.geometry.dispose();
          if (object.material) {
             if (Array.isArray(object.material)) {
               object.material.forEach(material => material.dispose());
             } else {
               object.material.dispose();
             }
          }
        }
      });
      pointLights.forEach(light => light.dispose());
      renderer.dispose();
    };
  }, []); // Run only once

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'black',
      color: '#f3f4f6',
      fontFamily: 'Inter, sans-serif',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {/* Div where the Three.js scene will be rendered */}
      <div ref={mountRef} style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 0
      }}></div>

      {/* Text content - positioned to the left of the cake */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        zIndex: 10,
        pointerEvents: 'none'
      }}>
        <div style={{
          width: window.innerWidth < 768 ? '100%' : '45%',
          display: 'flex',
          justifyContent: window.innerWidth < 768 ? 'center' : 'flex-end',
          paddingRight: window.innerWidth < 768 ? '0' : '6rem'
        }}>
          <div className="text-box">
            <h1 className="title">
              İyi ki Doğdun Ece!
            </h1>
            <p className="description">
              Nice senelere  Ece! Bu yeni yaşında hayatının her alanında daha da mutlu,
              daha başarılı, daha sağlıklı ve hayallerine bir adım daha yakın olmanı dilerim.
              Güzelliklerle dolu bir yıl seni beklesin!
            </p>
            <div className="age-section">
              <span className="emoji">🎉</span>
              <span className="age-text">21 Yaş</span>
              <span className="emoji">🎂</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100%',
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        textAlign: 'center',
        padding: '12px',
        color: '#9ca3af',
        fontSize: '12px',
        zIndex: 10
      }}>
        <p>&copy; {new Date().getFullYear()} Senin İçin Özel. Tüm Hakları Saklıdır.</p>
        <p style={{ marginTop: '4px' }}>Sevgiyle Tasarlandı</p>
      </footer>

      {/* Custom CSS styles */}
      <style>
        {`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;700;800&display=swap');
        body {
          font-family: 'Inter', sans-serif;
        }

        .text-box {
          text-align: center;
          background: rgba(17, 24, 39, 0.85);
          backdrop-filter: blur(12px);
          padding: 2.5rem;
          border-radius: 28px;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          border: 1px solid rgba(139, 92, 246, 0.6);
          animation: fade-in-left 1.5s ease-out forwards;
          max-width: 500px;
          pointer-events: auto;
        }

        .title {
          font-size: 3.5rem;
          font-weight: 800;
          background: linear-gradient(to right, #a855f7, #ec4899, #ef4444);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          margin-bottom: 2rem;
          filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
          animation: pulse 2s infinite;
          line-height: 1.1;
        }

        .description {
          font-size: 1.2rem;
          font-style: italic;
          line-height: 1.6;
          color: #e5e7eb;
          margin-bottom: 1.5rem;
        }

        .age-section {
          text-align: center;
        }

        .emoji {
          display: inline-block;
          font-size: 2.5rem;
          font-weight: bold;
          color: #fbbf24;
          animation: bounce 2s infinite;
        }

        .age-text {
          display: inline-block;
          font-size: 2rem;
          font-weight: 600;
          color: #f472b6;
          margin: 0 0.8rem;
        }

        /* Keyframes for animations */
        @keyframes fade-in-left {
          from { opacity: 0; transform: translateX(-50px); }
          to { opacity: 1; transform: translateX(0); }
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .text-box {
            max-width: 320px;
            padding: 1.5rem;
          }

          .title {
            font-size: 2rem;
            margin-bottom: 1rem;
          }

          .description {
            font-size: 0.9rem;
          }

          .emoji {
            font-size: 1.5rem;
          }

          .age-text {
            font-size: 1.2rem;
          }
        }

        @media (min-width: 768px) {
          .text-box {
            text-align: left;
          }

          .age-section {
            text-align: left;
          }
        }
        `}
      </style>
    </div>
  );
}

export default App;
